#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奇安信流量传感器登录模块
包含完整的登录逻辑和基础API调用功能
"""

import requests
import re
import random
import time
import ddddocr

class QianXinLoginSimulator:
    def __init__(self, base_url):
        """
        初始化登录模拟器
        
        Args:
            base_url (str): 目标服务器地址，如 https://*************
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.csrf_token = None
        self.session_cookie = None
        
        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
    
    def step1_get_login_page(self):
        """步骤1: 获取登录页面，获取初始session和csrf_token"""
        print("步骤1: 获取登录页面...")
        
        url = f"{self.base_url}/sensor/login"
        headers = {
            'Cache-Control': 'max-age=0',
            'Upgrade-Insecure-Requests': '1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Referer': f'{self.base_url}/sensor/home/<USER>'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        
        # 从响应中提取csrf_token
        csrf_match = re.search(r'content="([a-f0-9]{32})"', response.text)
        if csrf_match:
            self.csrf_token = csrf_match.group(1)
            print(f"获取到 CSRF Token: {self.csrf_token}")
        
        # 获取session cookie
        if 'session-sensor' in response.cookies:
            self.session_cookie = response.cookies['session-sensor']
            print(f"获取到 Session Cookie: {self.session_cookie}")
        
        return response.status_code == 200
    
    def step2_get_project_config(self):
        """步骤2: 获取项目配置文件"""
        print("步骤2: 获取项目配置...")
        
        timestamp = int(time.time() * 1000)
        url = f"{self.base_url}/sensor/project/project.config.json?v={timestamp}"
        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                config = response.json()
                print(f"项目版本: {config.get('version', 'Unknown')}")
                return True
            except:
                print("配置文件解析失败")
                return False
        return False
    
    def step3_get_favicon(self):
        """步骤3: 获取网站图标"""
        print("步骤3: 获取网站图标...")
        
        url = f"{self.base_url}/static/client/favicon.ico"
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Dest': 'image',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step4_check_login_status(self):
        """步骤4: 检查登录状态"""
        print("步骤4: 检查登录状态...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/admin/login?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"登录检查结果: {data.get('message', 'Unknown')}")
                return data.get('status') == 400  # 400表示未登录，这是正常的
            except:
                return False
        return False
    
    def step5_get_sitemap(self):
        """步骤5: 获取站点地图"""
        print("步骤5: 获取站点地图...")
        
        timestamp = int(time.time() * 1000)
        url = f"{self.base_url}/sensor/project/sitemap.json?v={timestamp}"
        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step6_check_updating_status(self):
        """步骤6: 检查系统更新状态"""
        print("步骤6: 检查系统更新状态...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/config/is_updating?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step7_switch_language(self):
        """步骤7: 切换语言设置"""
        print("步骤7: 切换语言设置...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/state/switch/language?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step8_get_login_microapp(self):
        """步骤8: 获取登录微应用"""
        print("步骤8: 获取登录微应用...")
        
        url = f"{self.base_url}/sensor/microapps/login-sensor/index.html?v=0.21.0-rc.0"
        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step9_get_logo(self):
        """步骤9: 获取系统Logo"""
        print("步骤9: 获取系统Logo...")
        
        url = f"{self.base_url}/skyeye/logo/skyeye-logo-big2.png"
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Dest': 'image',
            'Referer': f'{self.base_url}/sensor/login',
            'If-None-Match': '"64bf3e20-132e"',
            'If-Modified-Since': 'Tue, 25 Jul 2023 03:14:40 GMT'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        return response.status_code in [200, 304]
    
    def step10_get_license_info(self):
        """步骤10: 获取许可证信息"""
        print("步骤10: 获取许可证信息...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/config/unlogin_license?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step11_check_agency_status(self):
        """步骤11: 检查代理状态"""
        print("步骤11: 检查代理状态...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/config/is_agency?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step12_get_captcha(self, auto_recognize=True):
        """步骤12: 获取验证码"""
        print("步骤12: 获取验证码...")

        random_param = random.random()
        url = f"{self.base_url}/skyeye/admin/code?r={random_param}"
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Dest': 'image',
            'Referer': f'{self.base_url}/sensor/login'
        }

        response = self.session.get(url, headers=headers, verify=False)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            # 保存验证码图片
            with open('captcha.gif', 'wb') as f:
                f.write(response.content)
            print("验证码已保存为 captcha.gif")

            # 自动识别验证码
            if auto_recognize:
                try:
                    print("正在自动识别验证码...")
                    ocr = ddddocr.DdddOcr()
                    result = ocr.classification(response.content)
                    print(f"自动识别结果: {result}")
                    return True, result
                except Exception as e:
                    print(f"自动识别失败: {e}")
                    print("请手动查看 captcha.gif 文件")
                    return True, None
            else:
                return True, None

        return False

    def _login_once(self, username, password, auto_recognize=True, verbose=False):
        """
        执行一次完整的登录流程（内部方法，不包含重试逻辑）

        Args:
            username (str): 用户名
            password (str): 密码
            auto_recognize (bool): 是否自动识别验证码，默认True
            verbose (bool): 是否显示详细日志，默认False

        Returns:
            tuple: (success: bool, error_msg: str) 登录是否成功和错误信息
        """
        try:
            # 执行前11个步骤
            steps = [
                self.step1_get_login_page,
                self.step2_get_project_config,
                self.step3_get_favicon,
                self.step4_check_login_status,
                self.step5_get_sitemap,
                self.step6_check_updating_status,
                self.step7_switch_language,
                self.step8_get_login_microapp,
                self.step9_get_logo,
                self.step10_get_license_info,
                self.step11_check_agency_status
            ]

            # 执行前置步骤
            for i, step in enumerate(steps, 1):
                try:
                    success = step()
                    if not success and verbose:
                        print(f"步骤 {i} 执行失败，但继续执行...")
                    # 如果步骤1失败，直接返回失败
                    if i == 1 and not success:
                        return False, "获取登录页面失败"
                    time.sleep(0.1)  # 模拟真实的请求间隔
                except Exception as e:
                    if verbose:
                        print(f"步骤 {i} 执行异常: {e}")
                    # 如果关键步骤失败，直接返回失败
                    if i == 1:  # 步骤1是获取登录页面，这是关键步骤
                        return False, f"关键步骤异常: {e}"

            # 步骤12: 获取并识别验证码
            captcha_success, auto_captcha = self.step12_get_captcha(auto_recognize)

            if not captcha_success:
                return False, "获取验证码失败"

            # 确定最终使用的验证码
            final_captcha = auto_captcha

            if not final_captcha:
                return False, "验证码识别失败"

            # 执行登录
            login_success = self.step13_login(username, password, final_captcha)

            if login_success:
                if verbose:
                    print("登录成功!")
                return True, "登录成功"
            else:
                return False, "登录失败，可能是验证码错误或用户名密码错误"

        except Exception as e:
            return False, f"登录过程发生异常: {e}"

    def login(self, username, password, auto_recognize=True, verbose=False, max_retries=5):
        """
        带重试机制的登录函数

        Args:
            username (str): 用户名
            password (str): 密码
            auto_recognize (bool): 是否自动识别验证码，默认True
            verbose (bool): 是否显示详细日志，默认False
            max_retries (int): 最大重试次数，默认5次

        Returns:
            bool: 登录是否成功
        """
        if verbose:
            print("=" * 80)
            print("开始登录奇安信流量传感器（带重试机制）")
            print(f"最大重试次数: {max_retries}")
            print("=" * 80)

        for attempt in range(max_retries):
            if verbose:
                if attempt == 0:
                    print(f"第 {attempt + 1} 次尝试登录...")
                else:
                    print(f"\n第 {attempt + 1} 次尝试登录...")

            # 执行一次完整的登录流程
            success, error_msg = self._login_once(username, password, auto_recognize, verbose)

            if success:
                if verbose:
                    print("✅ 登录成功!")
                return True
            else:
                if verbose:
                    print(f"❌ 登录失败: {error_msg}")
                    if attempt < max_retries - 1:
                        print(f"等待2秒后重试... (剩余重试次数: {max_retries - attempt - 1})")
                    else:
                        print("已达到最大重试次数")

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    time.sleep(2)
                else:
                    if verbose:
                        print("=" * 80)
                        print("❌ 登录失败，已达到最大重试次数")
                        print("=" * 80)
                    return False

        return False

    def is_logged_in(self):
        """
        检查当前是否已登录

        Returns:
            bool: 是否已登录
        """
        try:
            random_param = random.random()
            url = f"{self.base_url}/skyeye/admin/login?csrf_token={self.csrf_token}&r={random_param}"
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': f'{self.base_url}/sensor/login'
            }

            response = self.session.get(url, headers=headers, verify=False)
            if response.status_code == 200:
                data = response.json()
                # 如果返回状态不是400，说明已登录
                return data.get('status') != 400
        except:
            pass
        return False

    def api_request(self, method, endpoint, **kwargs):
        """
        发送API请求的统一方法

        Args:
            method (str): HTTP方法 (GET, POST, PUT, DELETE等)
            endpoint (str): API端点路径，如 "/skyeye/alarm/alert_list"
            **kwargs: 其他请求参数，如 params, json, data, headers等

        Returns:
            requests.Response: HTTP响应对象
        """
        # 构建完整URL
        if endpoint.startswith('/'):
            url = f"{self.base_url}{endpoint}"
        else:
            url = f"{self.base_url}/{endpoint}"

        # 设置默认headers
        default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty'
        }

        # 合并headers
        headers = kwargs.get('headers', {})
        headers.update(default_headers)
        kwargs['headers'] = headers

        # 添加CSRF token到参数中（如果需要）
        if self.csrf_token and method.upper() in ['POST', 'PUT', 'DELETE']:
            if 'json' in kwargs:
                if isinstance(kwargs['json'], dict):
                    kwargs['json']['csrf_token'] = self.csrf_token
            elif 'data' in kwargs:
                if isinstance(kwargs['data'], dict):
                    kwargs['data']['csrf_token'] = self.csrf_token

        # 禁用SSL验证
        kwargs['verify'] = False

        # 发送请求
        return self.session.request(method, url, **kwargs)

    def encrypt_password(self):
        """加密密码 (模拟前端加密逻辑)"""
        return "U2FsdGVkX18woJynzevdp9AF+c6KpWiR0H+dfkYJfDo="
    
    def step13_login(self, username, password, captcha):
        """步骤13: 执行登录"""
        print("步骤13: 执行登录...")
        
        # 加密密码
        encrypted_password = self.encrypt_password()
        
        url = f"{self.base_url}/skyeye/admin/login"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Origin': self.base_url,
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        login_data = {
            "type_login": "sys",
            "username": username,
            "password": encrypted_password,
            "authcode": captcha,
            "csrf_token": self.csrf_token,
            "r": random.random()
        }
        
        response = self.session.post(url, headers=headers, json=login_data, verify=False)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"登录结果: {result.get('message', 'Unknown')}")
                
                if result.get('status') == 200:
                    print("登录成功!")
                    print(f"用户信息: {result.get('data', {})}")
                    return True
                else:
                    print(f"登录失败: {result.get('message', 'Unknown error')}")
                    return False
            except Exception as e:
                print(f"响应解析失败: {e}")
                return False
        
        return False



