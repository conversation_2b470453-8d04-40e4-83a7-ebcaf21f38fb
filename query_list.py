#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奇安信流量传感器查询列表模块
包含告警列表查询和其他查询功能
"""

import time
import random
from login import QianXinLoginSimulator

class AlertQueryManager:
    def __init__(self, login_simulator):
        """
        初始化查询管理器
        
        Args:
            login_simulator (QianXinLoginSimulator): 已登录的模拟器实例
        """
        self.simulator = login_simulator
    
    def get_alert_list(self, offset=1, limit=10, start_time=None, end_time=None, **filters):
        """
        获取告警列表
        
        Args:
            offset (int): 偏移量，默认1
            limit (int): 每页数量，默认10
            start_time (int): 开始时间戳（毫秒），默认None
            end_time (int): 结束时间戳（毫秒），默认None
            **filters: 其他过滤条件
            
        Returns:
            dict: 告警列表数据，如果请求失败返回None
        """
        # 如果没有指定时间范围，使用默认值（最近24小时）
        if end_time is None:
            end_time = int(time.time() * 1000)  # 当前时间戳（毫秒）
        if start_time is None:
            start_time = end_time - (24 * 60 * 60 * 1000)  # 24小时前
        
        # 构建查询参数
        params = {
            'offset': offset,
            'limit': limit,
            'order_by': 'access_time:desc',
            'is_accurate': 0,
            'data_source': 1,
            'start_time': start_time,
            'end_time': end_time,
            'csrf_token': self.simulator.csrf_token,
            'r': random.random()
        }
        
        # 添加默认的空参数（保持与原始请求一致）
        default_filters = {
            'host_state': '',
            'status': '',
            'ioc': '',
            'threat_name': '',
            'attack_stage': '',
            'x_forwarded_for': '',
            'host': '',
            'status_http': '',
            'alarm_source': '',
            'uri': '',
            'attck_org': '',
            'attck': '',
            'alert_rule': '',
            'attack_dimension': '',
            'is_web_attack': '',
            'sip': '',
            'dip': '',
            'sport': '',
            'dport': '',
            'dst_mac': '',
            'src_mac': '',
            'vlan_id': '',
            'marks': '',
            'vxlan_id': '',
            'start_update_time': '',
            'end_update_time': '',
            'threat_type': '',
            'hazard_level': '',
            'alarm_sip': '',
            'attack_sip': '',
            'alarm_id': '',
            'file_name': '',
            'file_md5': '',
            'file_type': '',
            'proto': '',
            'user_label': ''
        }
        
        # 合并默认过滤条件和用户提供的过滤条件
        default_filters.update(filters)
        params.update(default_filters)
        
        try:
            response = self.simulator.api_request('GET', '/skyeye/alarm/alert_list', params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"获取告警列表失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"获取告警列表异常: {e}")
            return None
    
    def get_high_risk_alerts(self, limit=10):
        """
        获取高危告警
        
        Args:
            limit (int): 返回数量限制
            
        Returns:
            dict: 高危告警数据
        """
        return self.get_alert_list(
            offset=1,
            limit=limit,
            hazard_level="高危"
        )
    
    def get_web_attacks(self, limit=10):
        """
        获取Web攻击告警
        
        Args:
            limit (int): 返回数量限制
            
        Returns:
            dict: Web攻击告警数据
        """
        return self.get_alert_list(
            offset=1,
            limit=limit,
            is_web_attack="1"
        )
    
    def get_unprocessed_alerts(self, limit=10):
        """
        获取未处置告警
        
        Args:
            limit (int): 返回数量限制
            
        Returns:
            dict: 未处置告警数据
        """
        return self.get_alert_list(
            offset=1,
            limit=limit,
            status="未处置"
        )
    
    def get_alerts_by_ip(self, source_ip=None, dest_ip=None, limit=10):
        """
        根据IP地址查询告警
        
        Args:
            source_ip (str): 源IP地址
            dest_ip (str): 目标IP地址
            limit (int): 返回数量限制
            
        Returns:
            dict: 告警数据
        """
        filters = {}
        if source_ip:
            filters['sip'] = source_ip
        if dest_ip:
            filters['dip'] = dest_ip
            
        return self.get_alert_list(
            offset=1,
            limit=limit,
            **filters
        )
    
    def get_alerts_by_time_range(self, hours_ago=1, limit=20):
        """
        获取指定时间范围内的告警
        
        Args:
            hours_ago (int): 多少小时前开始查询
            limit (int): 返回数量限制
            
        Returns:
            dict: 告警数据
        """
        current_time = int(time.time() * 1000)
        start_time = current_time - (hours_ago * 60 * 60 * 1000)
        
        return self.get_alert_list(
            offset=1,
            limit=limit,
            start_time=start_time,
            end_time=current_time
        )
    
    def get_alerts_by_threat_type(self, threat_type, limit=10):
        """
        根据威胁类型查询告警
        
        Args:
            threat_type (str): 威胁类型，如 "跨站脚本攻击（XSS）"
            limit (int): 返回数量限制
            
        Returns:
            dict: 告警数据
        """
        return self.get_alert_list(
            offset=1,
            limit=limit,
            threat_type=threat_type
        )
    
    def search_alerts(self, **search_criteria):
        """
        多条件搜索告警
        
        Args:
            **search_criteria: 搜索条件，支持所有过滤参数
            
        Returns:
            dict: 告警数据
        """
        # 设置默认值
        search_criteria.setdefault('offset', 1)
        search_criteria.setdefault('limit', 20)
        
        return self.get_alert_list(**search_criteria)
    
    def get_alert_statistics(self, hours=24):
        """
        获取告警统计信息
        
        Args:
            hours (int): 统计时间范围（小时）
            
        Returns:
            dict: 统计信息
        """
        # 获取指定时间范围内的所有告警
        alerts_data = self.get_alerts_by_time_range(hours, limit=1000)
        
        if not alerts_data or not alerts_data.get('items'):
            return {
                'total': 0,
                'high_risk': 0,
                'medium_risk': 0,
                'low_risk': 0,
                'web_attacks': 0,
                'unprocessed': 0
            }
        
        items = alerts_data.get('items', [])
        stats = {
            'total': alerts_data.get('total', 0),
            'high_risk': 0,
            'medium_risk': 0,
            'low_risk': 0,
            'web_attacks': 0,
            'unprocessed': 0,
            'threat_types': {},
            'attack_sources': {}
        }
        
        # 统计各种类型
        for item in items:
            # 危险级别统计
            hazard_level = item.get('hazard_level', '')
            if hazard_level == '高危':
                stats['high_risk'] += 1
            elif hazard_level == '中危':
                stats['medium_risk'] += 1
            elif hazard_level == '低危':
                stats['low_risk'] += 1
            
            # Web攻击统计
            if item.get('is_web_attack') == 1:
                stats['web_attacks'] += 1
            
            # 未处置统计
            if item.get('status') == '未处置':
                stats['unprocessed'] += 1
            
            # 威胁类型统计
            threat_type = item.get('threat_type', 'Unknown')
            stats['threat_types'][threat_type] = stats['threat_types'].get(threat_type, 0) + 1
            
            # 攻击源统计
            sip = item.get('sip', 'Unknown')
            stats['attack_sources'][sip] = stats['attack_sources'].get(sip, 0) + 1
        
        return stats
    
    def export_alerts_to_json(self, filename=None, **query_params):
        """
        导出告警数据到JSON文件
        
        Args:
            filename (str): 文件名，默认自动生成
            **query_params: 查询参数
            
        Returns:
            str: 导出的文件名
        """
        import json
        
        if filename is None:
            filename = f"alerts_export_{int(time.time())}.json"
        
        # 获取告警数据
        query_params.setdefault('limit', 1000)  # 默认导出1000条
        alerts_data = self.get_alert_list(**query_params)
        
        if alerts_data:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(alerts_data, f, ensure_ascii=False, indent=2)
            
            print(f"告警数据已导出到: {filename}")
            print(f"导出条数: {len(alerts_data.get('items', []))}")
            print(f"总条数: {alerts_data.get('total', 0)}")
            return filename
        else:
            print("没有获取到告警数据，导出失败")
            return None

def create_query_manager(base_url, username, password, max_retries=5, verbose=True):
    """
    创建查询管理器的便捷函数，支持自动重试

    Args:
        base_url (str): 服务器地址
        username (str): 用户名
        password (str): 密码
        max_retries (int): 最大重试次数，默认5次
        verbose (bool): 是否显示详细日志，默认True

    Returns:
        AlertQueryManager: 查询管理器实例，如果登录失败返回None
    """
    # 创建登录模拟器
    simulator = QianXinLoginSimulator(base_url)

    # 执行登录（带重试）
    if simulator.login(username, password, auto_recognize=True, verbose=verbose, max_retries=max_retries):
        if verbose:
            print("登录成功，创建查询管理器")
        return AlertQueryManager(simulator)
    else:
        if verbose:
            print("登录失败，无法创建查询管理器")
        return None
