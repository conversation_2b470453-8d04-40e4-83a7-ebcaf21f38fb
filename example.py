#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奇安信流量传感器使用示例
演示登录、查询告警列表、查询告警详情的完整流程
"""

from query_list import create_query_manager
from alert_detail import AlertDetailManager


def format_alert_fields(item):
    """
    格式化告警字段显示
    
    Args:
        item (dict): 告警项目数据
        
    Returns:
        dict: 格式化后的显示字段
    """
    # 基本字段
    alert_id = item.get('id', 'Unknown')
    threat_name = item.get('threat_name', 'Unknown')
    hazard_level = item.get('hazard_level', 'Unknown')
    host_state = item.get('host_state', 'Unknown')
    sip = item.get('sip', 'Unknown')
    dip = item.get('dip', 'Unknown')
    
    # 格式化危险级别显示（危急、高危、中危、低危）
    if hazard_level == '危急':
        hazard_display = f"🚨 {hazard_level}"
    elif hazard_level == '高危':
        hazard_display = f"🔴 {hazard_level}"
    elif hazard_level == '中危':
        hazard_display = f"🟡 {hazard_level}"
    elif hazard_level == '低危':
        hazard_display = f"🟢 {hazard_level}"
    else:
        hazard_display = f"⚪ {hazard_level}"
    
    # 格式化攻击结果显示（成功、企图、失败）
    if host_state == '成功':
        state_display = f"❌ 攻击成功"
    elif host_state == '企图':
        state_display = f"⚠️ 攻击企图"
    elif host_state == '失败':
        state_display = f"✅ 攻击失败"
    else:
        state_display = f"📊 {host_state}"
    
    return {
        'id': alert_id,
        'threat_name': threat_name,
        'hazard_level': hazard_level,
        'hazard_display': hazard_display,
        'host_state': host_state,
        'state_display': state_display,
        'sip': sip,
        'dip': dip
    }


def basic_example():
    """
    基本使用示例：登录 -> 查询列表 -> 查询详情
    """
    print("奇安信流量传感器基本使用示例")
    print("="*60)
    
    # 配置信息
    BASE_URL = "https://*************"
    USERNAME = "admin"
    PASSWORD = ""
    MAX_RETRIES = 5
    
    # 步骤1: 创建查询管理器（自动处理登录）
    print("步骤1: 登录系统...")
    query_manager = create_query_manager(
        base_url=BASE_URL,
        username=USERNAME,
        password=PASSWORD,
        max_retries=MAX_RETRIES,
        verbose=True
    )
    
    if not query_manager:
        print("❌ 登录失败，程序退出")
        return
    
    print("✅ 登录成功!")
    
    # 步骤2: 查询告警列表
    print("\n步骤2: 查询告警列表...")
    alerts = query_manager.get_alert_list()
    
    if not alerts or not alerts.get('items'):
        print("❌ 无法获取告警列表")
        return
    
    items = alerts['items']
    print(f"✅ 获取到 {len(items)} 条告警")
    
    # 显示告警列表
    print(f"\n📊 告警列表 (总数: {alerts.get('total', 0)}):")
    print("="*80)
    
    for i, item in enumerate(items, 1):
        formatted = format_alert_fields(item)
        
        print(f"{i}. ID: {formatted['id']}")
        print(f"   威胁: {formatted['threat_name']}")
        print(f"   危险级别: {formatted['hazard_display']}")
        print(f"   攻击结果: {formatted['state_display']}")
        print(f"   网络: {formatted['sip']} -> {formatted['dip']}")
        print()
    
    # 步骤3: 查询告警详情
    print("步骤3: 查询告警详情...")
    
    # 创建详情管理器（复用登录会话）
    detail_manager = AlertDetailManager(query_manager.simulator)
    
    # 查询前3个告警的详情
    for i, item in enumerate(items[:3], 1):
        alert_id = item.get('id')
        if not alert_id:
            print(f"❌ 告警 {i} 没有ID，跳过")
            continue
        
        print(f"\n🔍 查询告警 {i} 的详情 (ID: {alert_id})")
        print("-" * 50)
        
        # 获取基本信息
        basic_info = detail_manager.get_alert_basic_info(alert_id)
        if basic_info:
            print(f"✅ 规则名称: {basic_info['rule_name']}")
            print(f"✅ 源地址: {basic_info['source_ip']}:{basic_info['source_port']}")
            print(f"✅ 目标地址: {basic_info['dest_ip']}:{basic_info['dest_port']}")
            print(f"✅ 协议: {basic_info['protocol']}")
            
            if basic_info['is_weak_password']:
                print("⚠️  检测到弱密码攻击")
        else:
            print(f"❌ 无法获取告警 {alert_id} 的详情")
    
    print("\n✅ 基本示例完成!")


def advanced_example():
    """
    高级使用示例：不同类型的查询和统计
    """
    print("\n" + "="*60)
    print("高级使用示例")
    print("="*60)
    
    # 创建查询管理器
    query_manager = create_query_manager(
        base_url="https://*************",
        username="admin",
        password="",
        max_retries=5,
        verbose=False  # 简化日志
    )
    
    if not query_manager:
        print("❌ 登录失败")
        return
    
    # 1. 查询高危告警
    print("1. 🔴 查询高危告警:")
    high_risk_alerts = query_manager.get_high_risk_alerts(limit=3)
    if high_risk_alerts and high_risk_alerts.get('items'):
        for i, item in enumerate(high_risk_alerts['items'], 1):
            formatted = format_alert_fields(item)
            print(f"   {i}. {formatted['threat_name']} | {formatted['hazard_display']} | {formatted['state_display']}")
    else:
        print("   没有找到高危告警")
    
    # 2. 查询Web攻击
    print("\n2. 🌐 查询Web攻击:")
    web_attacks = query_manager.get_web_attacks(limit=3)
    if web_attacks and web_attacks.get('items'):
        for i, item in enumerate(web_attacks['items'], 1):
            formatted = format_alert_fields(item)
            print(f"   {i}. {formatted['threat_name']} | {formatted['hazard_display']} | {formatted['state_display']}")
    else:
        print("   没有找到Web攻击")
    
    # 3. 查询最近1小时的告警
    print("\n3. ⏰ 查询最近1小时的告警:")
    recent_alerts = query_manager.get_alerts_by_time_range(hours_ago=1, limit=3)
    if recent_alerts and recent_alerts.get('items'):
        for i, item in enumerate(recent_alerts['items'], 1):
            formatted = format_alert_fields(item)
            print(f"   {i}. {formatted['threat_name']} | {formatted['hazard_display']} | {formatted['state_display']}")
    else:
        print("   最近1小时没有告警")
    
    # 4. 获取统计信息
    print("\n4. 📈 获取统计信息:")
    stats = query_manager.get_alert_statistics(hours=24)
    print(f"   最近24小时统计:")
    print(f"   总告警数: {stats['total']}")
    print(f"   高危: {stats['high_risk']}")
    print(f"   中危: {stats['medium_risk']}")
    print(f"   低危: {stats['low_risk']}")
    print(f"   Web攻击: {stats['web_attacks']}")
    print(f"   未处置: {stats['unprocessed']}")
    
    print("\n✅ 高级示例完成!")


def export_example():
    """
    数据导出示例
    """
    print("\n" + "="*60)
    print("数据导出示例")
    print("="*60)
    
    # 创建查询管理器
    query_manager = create_query_manager(
        base_url="https://*************",
        username="admin",
        password="",
        max_retries=5,
        verbose=False
    )
    
    if not query_manager:
        print("❌ 登录失败")
        return
    
    # 1. 导出告警列表
    print("1. 💾 导出告警列表:")
    filename = query_manager.export_alerts_to_json(
        filename="alerts_export.json",
        limit=20
    )
    if filename:
        print(f"   ✅ 告警列表已导出到: {filename}")
    
    # 2. 导出告警详情
    print("\n2. 💾 导出告警详情:")
    
    # 获取一些告警ID
    alerts = query_manager.get_alert_list(limit=3)
    if alerts and alerts.get('items'):
        detail_manager = AlertDetailManager(query_manager.simulator)
        
        for i, item in enumerate(alerts['items'], 1):
            alert_id = item.get('id')
            if alert_id:
                filename = detail_manager.export_alert_detail_to_json(
                    alert_id,
                    filename=f"alert_{alert_id}_detail.json"
                )
                if filename:
                    print(f"   ✅ 告警 {alert_id} 详情已导出到: {filename}")
    
    print("\n✅ 导出示例完成!")


def main():
    """
    主函数
    """
    print("奇安信流量传感器使用示例")
    print("="*80)
    print("包含功能:")
    print("- 自动登录（支持验证码识别和重试）")
    print("- 告警列表查询（支持多种过滤条件）")
    print("- 告警详情查询（根据ID获取详细信息）")
    print("- 数据导出（JSON格式）")
    print("="*80)
    
    try:
        # 运行基本示例
        basic_example()
        
        # 运行高级示例
        advanced_example()
        
        # # 运行导出示例
        # export_example()
        
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
    
    print("\n" + "="*80)
    print("示例程序运行完成!")
    print("="*80)


if __name__ == "__main__":
    main()
